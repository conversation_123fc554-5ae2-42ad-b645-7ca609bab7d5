/**
 * Minecraft Server Monitor App
 * Application pour surveiller les serveurs Minecraft
 *
 * @format
 */

import React, { useState, useEffect } from 'react';
import {
  StatusBar,
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  ScrollView,
  SafeAreaView,
  FlatList,
} from 'react-native';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface ServerData {
  online: boolean;
  hostname?: string;
  players: {
    online: number;
    max: number;
    list?: string[];
  };
  version?: string;
  motd?: {
    clean: string[];
  };
}

interface SavedServer {
  id: string;
  address: string;
  name?: string;
  lastChecked: string;
  lastStatus: 'online' | 'offline';
  lastPlayerCount?: number;
}

const STORAGE_KEY = '@minecraft_servers';

function App(): React.JSX.Element {
  const [serverIP, setServerIP] = useState<string>('');
  const [serverData, setServerData] = useState<ServerData | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [savedServers, setSavedServers] = useState<SavedServer[]>([]);
  const [activeTab, setActiveTab] = useState<'check' | 'servers'>('check');

  // Charger les serveurs sauvegardés au démarrage
  useEffect(() => {
    loadSavedServers();
  }, []);

  const loadSavedServers = async (): Promise<void> => {
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEY);
      if (stored) {
        setSavedServers(JSON.parse(stored));
      }
    } catch (error) {
      console.error('Erreur lors du chargement des serveurs:', error);
    }
  };

  const saveServerToStorage = async (servers: SavedServer[]): Promise<void> => {
    try {
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(servers));
      setSavedServers(servers);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
    }
  };

  const addOrUpdateServer = async (address: string, data: ServerData): Promise<void> => {
    const existingIndex = savedServers.findIndex(s => s.address === address);
    const serverInfo: SavedServer = {
      id: existingIndex >= 0 ? savedServers[existingIndex].id : Date.now().toString(),
      address,
      name: data.hostname || address,
      lastChecked: new Date().toISOString(),
      lastStatus: data.online ? 'online' : 'offline',
      lastPlayerCount: data.players?.online,
    };

    let updatedServers: SavedServer[];
    if (existingIndex >= 0) {
      updatedServers = [...savedServers];
      updatedServers[existingIndex] = serverInfo;
    } else {
      updatedServers = [...savedServers, serverInfo];
    }

    await saveServerToStorage(updatedServers);
  };

  const removeServer = async (id: string): Promise<void> => {
    Alert.alert(
      'Supprimer le serveur',
      'Êtes-vous sûr de vouloir supprimer ce serveur ?',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Supprimer',
          style: 'destructive',
          onPress: async () => {
            const updatedServers = savedServers.filter(s => s.id !== id);
            await saveServerToStorage(updatedServers);
          },
        },
      ]
    );
  };

  const checkServerStatus = async (): Promise<void> => {
    if (!serverIP.trim()) {
      Alert.alert('Erreur', 'Veuillez entrer une adresse IP de serveur');
      return;
    }

    setLoading(true);
    setError('');
    setServerData(null);

    try {
      // Utilisation de l'API mcsrvstat.us pour obtenir les informations du serveur
      const response = await axios.get<ServerData>(
        `https://api.mcsrvstat.us/3/${serverIP.trim()}`,
      );

      setServerData(response.data);
      
      // Ajouter ou mettre à jour le serveur dans la liste sauvegardée
      await addOrUpdateServer(serverIP.trim(), response.data);

      if (!response.data.online) {
        setError('Le serveur est hors ligne ou l\'adresse IP est incorrecte');
      }
    } catch (err) {
      setError(
        'Erreur lors de la vérification du serveur. Vérifiez votre connexion internet.',
      );
      console.error('Erreur:', err);
    } finally {
      setLoading(false);
    }
  };

  const checkSavedServer = async (address: string): Promise<void> => {
    setServerIP(address);
    setActiveTab('check');
    // Attendre que le state soit mis à jour avant de vérifier
    setTimeout(() => {
      checkServerStatus();
    }, 100);
  };

  const formatPlayerList = (players: ServerData['players']): string => {
    if (!players || !players.list || players.list.length === 0) {
      return 'Aucun joueur en ligne';
    }
    return players.list.join(', ');
  };

  const formatLastChecked = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'À l\'instant';
    if (diffMins < 60) return `Il y a ${diffMins} min`;
    if (diffMins < 1440) return `Il y a ${Math.floor(diffMins / 60)} h`;
    return `Il y a ${Math.floor(diffMins / 1440)} j`;
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1a1a2e" />
      
      <View style={styles.header}>
        <Text style={styles.title}>🎮 Minecraft Server Monitor</Text>
        <Text style={styles.subtitle}>Surveillez vos serveurs Minecraft</Text>
      </View>

      {/* Onglets de navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'check' && styles.activeTab]}
          onPress={() => setActiveTab('check')}>
          <Text style={[styles.tabText, activeTab === 'check' && styles.activeTabText]}>
            🔍 Vérifier
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'servers' && styles.activeTab]}
          onPress={() => setActiveTab('servers')}>
          <Text style={[styles.tabText, activeTab === 'servers' && styles.activeTabText]}>
            📋 Serveurs ({savedServers.length})
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView contentContainerStyle={styles.scrollContainer}>
        
        {/* Onglet Vérification */}
        {activeTab === 'check' && (
          <View>
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Adresse IP du serveur:</Text>
              <TextInput
                style={styles.input}
                value={serverIP}
                onChangeText={setServerIP}
                placeholder="Ex: mc.hypixel.net ou *************:25565"
                placeholderTextColor="#666"
                autoCapitalize="none"
                autoCorrect={false}
              />

              <TouchableOpacity
                style={[styles.button, loading && styles.buttonDisabled]}
                onPress={checkServerStatus}
                disabled={loading}>
                {loading ? (
                  <ActivityIndicator color="#fff" />
                ) : (
                  <Text style={styles.buttonText}>Vérifier le serveur</Text>
                )}
              </TouchableOpacity>
            </View>

            {error ? (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>❌ {error}</Text>
              </View>
            ) : null}

            {serverData ? (
              <View style={styles.serverInfoContainer}>
                <Text style={styles.serverTitle}>
                  {serverData.online ? '✅ Serveur en ligne' : '❌ Serveur hors ligne'}
                </Text>

                <View style={styles.infoCard}>
                  <Text style={styles.infoLabel}>Adresse:</Text>
                  <Text style={styles.infoValue}>
                    {serverData.hostname || serverIP}
                  </Text>
                </View>

                {serverData.online && (
                  <View>
                    <View style={styles.infoCard}>
                      <Text style={styles.infoLabel}>Joueurs en ligne:</Text>
                      <Text style={styles.playersCount}>
                        {serverData.players.online} / {serverData.players.max}
                      </Text>
                    </View>

                    {serverData.version ? (
                      <View style={styles.infoCard}>
                        <Text style={styles.infoLabel}>Version:</Text>
                        <Text style={styles.infoValue}>{serverData.version}</Text>
                      </View>
                    ) : null}

                    {serverData.motd && serverData.motd.clean ? (
                      <View style={styles.infoCard}>
                        <Text style={styles.infoLabel}>Message du jour:</Text>
                        <Text style={styles.infoValue}>
                          {serverData.motd.clean.join(' ')}
                        </Text>
                      </View>
                    ) : null}

                    {serverData.players &&
                    serverData.players.list &&
                    serverData.players.list.length > 0 ? (
                      <View style={styles.infoCard}>
                        <Text style={styles.infoLabel}>Joueurs connectés:</Text>
                        <Text style={styles.playersList}>
                          {formatPlayerList(serverData.players)}
                        </Text>
                      </View>
                    ) : null}
                  </View>
                )}
              </View>
            ) : null}
          </View>
        )}

        {/* Onglet Serveurs sauvegardés */}
        {activeTab === 'servers' && (
          <View style={styles.serversContainer}>
            {savedServers.length === 0 ? (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>🎮</Text>
                <Text style={styles.emptyTitle}>Aucun serveur sauvegardé</Text>
                <Text style={styles.emptySubtitle}>
                  Vérifiez un serveur pour l'ajouter automatiquement à votre liste
                </Text>
              </View>
            ) : (
              <FlatList
                data={savedServers}
                keyExtractor={(item) => item.id}
                renderItem={({ item }) => (
                  <View style={styles.serverCard}>
                    <View style={styles.serverCardHeader}>
                      <View style={styles.serverInfo}>
                        <Text style={styles.serverName}>{item.name}</Text>
                        <Text style={styles.serverAddress}>{item.address}</Text>
                      </View>
                      <View style={styles.serverStatus}>
                        <Text style={[
                          styles.statusIndicator,
                          item.lastStatus === 'online' ? styles.statusOnline : styles.statusOffline
                        ]}>
                          {item.lastStatus === 'online' ? '🟢' : '🔴'}
                        </Text>
                      </View>
                    </View>

                    <View style={styles.serverCardBody}>
                      <Text style={styles.lastChecked}>
                        Dernière vérification: {formatLastChecked(item.lastChecked)}
                      </Text>
                      {item.lastPlayerCount !== undefined && (
                        <Text style={styles.playerCount}>
                          👥 {item.lastPlayerCount} joueurs
                        </Text>
                      )}
                    </View>

                    <View style={styles.serverCardActions}>
                      <TouchableOpacity
                        style={styles.checkButton}
                        onPress={() => checkSavedServer(item.address)}>
                        <Text style={styles.checkButtonText}>🔍 Vérifier</Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={styles.deleteButton}
                        onPress={() => removeServer(item.id)}>
                        <Text style={styles.deleteButtonText}>🗑️ Supprimer</Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                )}
                showsVerticalScrollIndicator={false}
              />
            )}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a2e',
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
    marginTop: 20,
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#aaa',
    marginTop: 5,
    textAlign: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#16213e',
    marginHorizontal: 20,
    borderRadius: 10,
    marginBottom: 20,
    padding: 4,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: '#0f3460',
  },
  tabText: {
    fontSize: 16,
    color: '#aaa',
    fontWeight: '500',
  },
  activeTabText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    color: '#fff',
    marginBottom: 10,
    fontWeight: '600',
  },
  input: {
    backgroundColor: '#16213e',
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
    color: '#fff',
    borderWidth: 1,
    borderColor: '#0f3460',
    marginBottom: 15,
  },
  button: {
    backgroundColor: '#0f3460',
    borderRadius: 10,
    padding: 15,
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  buttonDisabled: {
    backgroundColor: '#666',
  },
  buttonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  errorContainer: {
    backgroundColor: '#ff4757',
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
  },
  errorText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
  },
  serverInfoContainer: {
    backgroundColor: '#16213e',
    borderRadius: 15,
    padding: 20,
    borderWidth: 1,
    borderColor: '#0f3460',
  },
  serverTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#4cd137',
    textAlign: 'center',
    marginBottom: 20,
  },
  infoCard: {
    backgroundColor: '#1a1a2e',
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#0f3460',
  },
  infoLabel: {
    fontSize: 14,
    color: '#aaa',
    marginBottom: 5,
  },
  infoValue: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '500',
  },
  playersCount: {
    fontSize: 24,
    color: '#4cd137',
    fontWeight: 'bold',
  },
  playersList: {
    fontSize: 14,
    color: '#fff',
    lineHeight: 20,
  },
