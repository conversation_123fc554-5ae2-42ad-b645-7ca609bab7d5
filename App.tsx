/**
 * Minecraft Server Monitor App
 * Application pour surveiller les serveurs Minecraft
 *
 * @format
 */

import React, { useState } from 'react';
import {
  StatusBar,
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import axios from 'axios';

interface ServerData {
  online: boolean;
  hostname?: string;
  players: {
    online: number;
    max: number;
    list?: string[];
  };
  version?: string;
  motd?: {
    clean: string[];
  };
}

function App(): React.JSX.Element {
  const [serverIP, setServerIP] = useState<string>('');
  const [serverData, setServerData] = useState<ServerData | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');

  const checkServerStatus = async (): Promise<void> => {
    if (!serverIP.trim()) {
      Alert.alert('Erreur', 'Veuillez entrer une adresse IP de serveur');
      return;
    }

    setLoading(true);
    setError('');
    setServerData(null);

    try {
      // Utilisation de l'API mcsrvstat.us pour obtenir les informations du serveur
      const response = await axios.get<ServerData>(
        `https://api.mcsrvstat.us/3/${serverIP.trim()}`,
      );

      if (response.data.online) {
        setServerData(response.data);
      } else {
        setError('Le serveur est hors ligne ou l\'adresse IP est incorrecte');
      }
    } catch (err) {
      setError(
        'Erreur lors de la vérification du serveur. Vérifiez votre connexion internet.',
      );
      console.error('Erreur:', err);
    } finally {
      setLoading(false);
    }
  };

  const formatPlayerList = (players: ServerData['players']): string => {
    if (!players || !players.list || players.list.length === 0) {
      return 'Aucun joueur en ligne';
    }
    return players.list.join(', ');
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1a1a2e" />
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.header}>
          <Text style={styles.title}>🎮 Minecraft Server Monitor</Text>
          <Text style={styles.subtitle}>Surveillez vos serveurs Minecraft</Text>
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Adresse IP du serveur:</Text>
          <TextInput
            style={styles.input}
            value={serverIP}
            onChangeText={setServerIP}
            placeholder="Ex: mc.hypixel.net ou *************:25565"
            placeholderTextColor="#666"
            autoCapitalize="none"
            autoCorrect={false}
          />

          <TouchableOpacity
            style={[styles.button, loading && styles.buttonDisabled]}
            onPress={checkServerStatus}
            disabled={loading}>
            {loading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.buttonText}>Vérifier le serveur</Text>
            )}
          </TouchableOpacity>
        </View>

        {error ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>❌ {error}</Text>
          </View>
        ) : null}

        {serverData ? (
          <View style={styles.serverInfoContainer}>
            <Text style={styles.serverTitle}>✅ Serveur en ligne</Text>

            <View style={styles.infoCard}>
              <Text style={styles.infoLabel}>Adresse:</Text>
              <Text style={styles.infoValue}>
                {serverData.hostname || serverIP}
              </Text>
            </View>

            <View style={styles.infoCard}>
              <Text style={styles.infoLabel}>Joueurs en ligne:</Text>
              <Text style={styles.playersCount}>
                {serverData.players.online} / {serverData.players.max}
              </Text>
            </View>

            {serverData.version ? (
              <View style={styles.infoCard}>
                <Text style={styles.infoLabel}>Version:</Text>
                <Text style={styles.infoValue}>{serverData.version}</Text>
              </View>
            ) : null}

            {serverData.motd && serverData.motd.clean ? (
              <View style={styles.infoCard}>
                <Text style={styles.infoLabel}>Message du jour:</Text>
                <Text style={styles.infoValue}>
                  {serverData.motd.clean.join(' ')}
                </Text>
              </View>
            ) : null}

            {serverData.players &&
            serverData.players.list &&
            serverData.players.list.length > 0 ? (
              <View style={styles.infoCard}>
                <Text style={styles.infoLabel}>Joueurs connectés:</Text>
                <Text style={styles.playersList}>
                  {formatPlayerList(serverData.players)}
                </Text>
              </View>
            ) : null}
          </View>
        ) : null}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a2e',
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
    marginTop: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#aaa',
    marginTop: 5,
    textAlign: 'center',
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    color: '#fff',
    marginBottom: 10,
    fontWeight: '600',
  },
  input: {
    backgroundColor: '#16213e',
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
    color: '#fff',
    borderWidth: 1,
    borderColor: '#0f3460',
    marginBottom: 15,
  },
  button: {
    backgroundColor: '#0f3460',
    borderRadius: 10,
    padding: 15,
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  buttonDisabled: {
    backgroundColor: '#666',
  },
  buttonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  errorContainer: {
    backgroundColor: '#ff4757',
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
  },
  errorText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
  },
  serverInfoContainer: {
    backgroundColor: '#16213e',
    borderRadius: 15,
    padding: 20,
    borderWidth: 1,
    borderColor: '#0f3460',
  },
  serverTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#4cd137',
    textAlign: 'center',
    marginBottom: 20,
  },
  infoCard: {
    backgroundColor: '#1a1a2e',
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#0f3460',
  },
  infoLabel: {
    fontSize: 14,
    color: '#aaa',
    marginBottom: 5,
  },
  infoValue: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '500',
  },
  playersCount: {
    fontSize: 24,
    color: '#4cd137',
    fontWeight: 'bold',
  },
  playersList: {
    fontSize: 14,
    color: '#fff',
    lineHeight: 20,
  },
});

export default App;
