# 🎮 Minecraft Server Monitor

Une application React Native pour surveiller le statut des serveurs Minecraft en temps réel.

## 📱 Fonctionnalités

- ✅ Vérification du statut en ligne/hors ligne des serveurs
- 👥 Affichage du nombre de joueurs connectés
- 🎮 Information sur la version du serveur
- 💬 Affichage du message du jour (MOTD)
- 📋 Liste des joueurs connectés (si disponible)
- 🌙 Interface sombre moderne

## 🚀 Installation et utilisation

### Prérequis

- Node.js (version 14 ou supérieure)
- React Native CLI
- Android Studio avec un émulateur Android
- SDK Android configuré

### Installation

1. Clonez ou téléchargez le projet
2. Installez les dépendances :
   ```bash
   npm install
   ```

3. Configurez le SDK Android :
   - Créez le fichier `android/local.properties`
   - Ajoutez la ligne : `sdk.dir=CHEMIN_VERS_VOTRE_SDK_ANDROID`

### Lancement de l'application

1. Démarrez votre émulateur Android
2. Lancez l'application :
   ```bash
   npx react-native run-android
   ```

## 🎯 Comment utiliser l'application

1. **Saisir l'adresse du serveur** : Entrez l'IP ou le nom de domaine du serveur Minecraft
   - Exemples : `mc.hypixel.net`, `*************:25565`

2. **Vérifier le statut** : Appuyez sur "Vérifier le serveur"

3. **Consulter les informations** : L'application affichera :
   - Statut en ligne/hors ligne
   - Nombre de joueurs connectés
   - Version du serveur
   - Message du jour
   - Liste des joueurs (si disponible)

## 🔧 API utilisée

L'application utilise l'API gratuite [mcsrvstat.us](https://mcsrvstat.us/) pour obtenir les informations des serveurs Minecraft.

## 🧪 Test de l'API

Vous pouvez tester l'API avec le script fourni :
```bash
node test-api.js
```

## 📱 Serveurs de test

Voici quelques serveurs populaires pour tester l'application :

- **Hypixel** : `mc.hypixel.net`
- **Mineplex** : `us.mineplex.com`
- **CubeCraft** : `play.cubecraft.net`

## 🛠️ Technologies utilisées

- React Native 0.80
- TypeScript
- Axios pour les requêtes HTTP
- API mcsrvstat.us

## 📝 Notes

- L'application nécessite une connexion internet pour fonctionner
- Certains serveurs peuvent bloquer les requêtes de statut
- Les informations affichées dépendent de la configuration du serveur
