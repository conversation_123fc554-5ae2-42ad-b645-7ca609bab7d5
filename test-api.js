// Script de test pour vérifier l'API de statut des serveurs Minecraft
const axios = require('axios');

async function testMinecraftAPI() {
  console.log('🧪 Test de l\'API Minecraft Server Status...\n');
  
  // Test avec un serveur populaire (Hypixel)
  try {
    console.log('📡 Test avec mc.hypixel.net...');
    const response = await axios.get('https://api.mcsrvstat.us/3/mc.hypixel.net');
    
    if (response.data.online) {
      console.log('✅ Serveur en ligne !');
      console.log(`👥 Joueurs: ${response.data.players.online}/${response.data.players.max}`);
      console.log(`🎮 Version: ${response.data.version || 'Non spécifiée'}`);
      if (response.data.motd && response.data.motd.clean) {
        console.log(`💬 MOTD: ${response.data.motd.clean.join(' ')}`);
      }
    } else {
      console.log('❌ Serveur hors ligne');
    }
  } catch (error) {
    console.error('❌ Erreur lors du test:', error.message);
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Test avec un serveur qui n'existe probablement pas
  try {
    console.log('📡 Test avec un serveur inexistant...');
    const response = await axios.get('https://api.mcsrvstat.us/3/serveur-inexistant-123456.com');
    
    if (response.data.online) {
      console.log('✅ Serveur en ligne (surprenant !)');
    } else {
      console.log('❌ Serveur hors ligne (comme attendu)');
    }
  } catch (error) {
    console.error('❌ Erreur lors du test:', error.message);
  }
  
  console.log('\n🎯 Tests terminés !');
}

testMinecraftAPI();
